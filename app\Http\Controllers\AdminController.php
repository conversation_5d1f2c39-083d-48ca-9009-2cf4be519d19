<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Settings;
use App\Models\AdminUser;
use App\Services\StorageService;

/**
 * Admin Controller
 * Handles admin panel API endpoints
 */
final class AdminController extends Controller
{
    private StorageService $storageService;

    public function __construct(StorageService $storageService)
    {
        $this->storageService = $storageService;
    }

    public function getSettings(): array
    {
        try {
            // Get admin user data from database
            $adminUser = null;
            $username = $_SESSION['admin_user'] ?? 'admin';

            if ($username) {
                $adminUser = AdminUser::findByUsername($username);
            }

            // Get other settings from Settings model
            $settings = Settings::getAll();

            // Convert to simple key-value format for frontend
            $settingsData = [];
            foreach ($settings as $key => $value) {
                $settingsData[$key] = $value;
            }

            // Provide default values if settings don't exist
            $defaultSettings = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'notification_email' => '<EMAIL>',
                'site_name' => 'DrxDion',
                'timezone' => 'Europe/Istanbul'
            ];

            // Merge with defaults first, then override with actual settings
            $result = array_merge($defaultSettings, $settingsData);

            // Override admin data with database values if available
            if ($adminUser) {
                $result['username'] = $adminUser->getUsername();
                $result['email'] = $adminUser->getEmail();
            }

            return [
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to load settings: ' . $e->getMessage()
            ];
        }
    }

    public function updateSettings(array $data): array
    {
        try {
            if (empty($data)) {
                return [
                    'success' => false,
                    'error' => 'No data provided'
                ];
            }

            // Get current admin user
            $currentUsername = $_SESSION['admin_user'] ?? 'admin';
            $currentUser = AdminUser::findByUsername($currentUsername);

            if (!$currentUser) {
                return [
                    'success' => false,
                    'error' => 'Admin user not found'
                ];
            }

            // Handle admin profile updates (username and email) - NO PASSWORD REQUIRED
            if (isset($data['username']) || isset($data['email'])) {
                $newUsername = $data['username'] ?? $currentUser->getUsername();
                $newEmail = $data['email'] ?? $currentUser->getEmail();

                // Update admin profile in database (no password verification needed for profile data)
                if (!$currentUser->updateProfile($newUsername, $newEmail)) {
                    return [
                        'success' => false,
                        'error' => 'Failed to update admin profile'
                    ];
                }

                // Update session if username changed
                if ($newUsername !== $currentUsername) {
                    $_SESSION['admin_user'] = $newUsername;
                }
            }

            // Handle password update - ONLY if new password is provided
            if (!empty($data['newPassword'])) {
                // Current password is required for password changes
                if (empty($data['currentPassword'])) {
                    return [
                        'success' => false,
                        'error' => 'Current password is required to change password'
                    ];
                }

                // Verify current password for password changes
                if (!$currentUser->verifyPassword($data['currentPassword'])) {
                    return [
                        'success' => false,
                        'error' => 'Current password is incorrect'
                    ];
                }

                // Update password
                $hashedPassword = password_hash($data['newPassword'], PASSWORD_DEFAULT);
                if (!$currentUser->updatePassword($hashedPassword)) {
                    return [
                        'success' => false,
                        'error' => 'Failed to update password'
                    ];
                }
            }

            // Update other settings (notification email, site settings, etc.)
            $settingsToUpdate = [];
            $envToUpdate = [];
            $allowedSettings = ['notification_email', 'site_name', 'timezone'];

            foreach ($allowedSettings as $key) {
                if (isset($data[$key])) {
                    $settingsToUpdate[$key] = $data[$key];

                    // Also update .env file for notification email only
                    if ($key === 'notification_email') {
                        $envToUpdate['NOTIFICATION_EMAIL'] = $data[$key];
                        $envToUpdate['ORDER_NOTIFICATION_TO'] = $data[$key]; // Backward compatibility
                    }
                }
            }

            // Update .env file first
            if (!empty($envToUpdate)) {
                \App\Utils\EnvManager::setMultiple($envToUpdate);
            }

            // Update settings storage
            if (!empty($settingsToUpdate)) {
                Settings::updateMultiple($settingsToUpdate);
            }

            return [
                'success' => true,
                'message' => 'Settings updated successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to update settings: ' . $e->getMessage()
            ];
        }
    }

    public function getStorageStatus(array $request = []): void
    {
        header('Content-Type: application/json');

        try {
            // Get storage status from StorageService
            $status = $this->storageService->getStatus();
            $healthMetrics = $this->storageService->getHealthMetrics();
            $syncStats = $this->storageService->getSyncStats();

            // Get recent orders for display
            $recentOrders = $this->getRecentOrders();

            // Get file status information
            $fileStatus = $this->getFileStatus();

            // Build response in the format expected by JavaScript
            $responseData = [
                'repository_type' => 'Smart Repository', // Default to Smart for now
                'storage' => [
                    'mysql_available' => $status['mysql']['connected'] ?? false,
                    'json_available' => $status['json']['connected'] ?? false,
                    'primary_storage' => ($status['mysql']['connected'] ?? false) ? 'mysql' : 'json'
                ],
                'data_consistency' => [
                    'is_consistent' => !($syncStats['sync_needed'] ?? true),
                    'difference' => $syncStats['difference'] ?? 0
                ],
                'order_statistics' => [
                    'mysql_count' => $syncStats['mysql_records'] ?? 0,
                    'json_count' => $syncStats['json_records'] ?? 0,
                    'total_orders' => max($syncStats['mysql_records'] ?? 0, $syncStats['json_records'] ?? 0),
                    'pending_orders' => 0, // Will be calculated from recent orders
                    'completed_orders' => 0 // Will be calculated from recent orders
                ],
                'recent_orders' => $recentOrders,
                'file_status' => $fileStatus,
                'last_sync' => $status['last_sync']
            ];

            echo json_encode([
                'success' => true,
                'data' => $responseData
            ]);
            return;
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to get storage status: ' . $e->getMessage()
            ]);
            return;
        }
    }

    public function syncStorage(array $data): array
    {
        try {
            // CSRF protection
            if (!$this->validateCSRF()) {
                return [
                    'success' => false,
                    'error' => 'Invalid CSRF token'
                ];
            }

            $forceReplace = ($data['force_replace'] ?? false) === true;

            $result = $this->storageService->sync($forceReplace);

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'Storage sync completed successfully',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error'] ?? 'Sync failed',
                    'data' => $result
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to sync storage: ' . $e->getMessage()
            ];
        }
    }

    public function login(): void
    {
        header('Content-Type: application/json');

        try {
            // Get POST data
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Invalid JSON data']);
                return;
            }

            $username = $data['username'] ?? '';
            $password = $data['password'] ?? '';

            // Simple hardcoded admin credentials for now
            if ($username === 'admin' && $password === 'admin123') {
                // Set session
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_user'] = $username;
                $_SESSION['login_time'] = time();

                echo json_encode([
                    'success' => true,
                    'message' => 'Login successful',
                    'redirect' => '/admin/orders'
                ]);
            } else {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid username or password'
                ]);
            }
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Server error: ' . $e->getMessage()
            ]);
        }
    }

    public function logout(): void
    {
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        try {
            // Destroy session
            session_destroy();

            if ($isAjax || $_SERVER['REQUEST_METHOD'] === 'POST') {
                // AJAX or POST request - return JSON
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Logout successful',
                    'redirect' => '/admin/login'
                ]);
            } else {
                // GET request - redirect directly
                header('Location: /admin/login');
                exit;
            }
        } catch (\Exception $e) {
            if ($isAjax || $_SERVER['REQUEST_METHOD'] === 'POST') {
                header('Content-Type: application/json');
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Server error: ' . $e->getMessage()
                ]);
            } else {
                header('Location: /admin/login?error=logout_failed');
                exit;
            }
        }
    }



    private function getRecentOrders(): array
    {
        try {
            // Get recent orders using the Order model
            $orders = \App\Models\Order::getAll();

            // Sort by created_at descending and take first 10
            usort($orders, function ($a, $b) {
                $dateA = strtotime($a['created_at'] ?? '1970-01-01');
                $dateB = strtotime($b['created_at'] ?? '1970-01-01');
                return $dateB <=> $dateA;
            });

            $recentOrders = array_slice($orders, 0, 20); // Show more recent orders

            // Return in the format expected by JavaScript
            return array_map(function ($order) {
                return [
                    'id' => $order['id'] ?? '',
                    'customer_name' => $order['customer_name'] ?? 'Unknown',
                    'total_price' => $order['total_price'] ?? 0,
                    'payment_status' => $order['payment_status'] ?? 'pending',
                    'created_at' => $order['created_at'] ?? null
                ];
            }, $recentOrders);
        } catch (\Exception $e) {
            return [];
        }
    }

    private function getFileStatus(): array
    {
        try {
            $dataDir = dirname(__DIR__, 2) . '/data';
            $ordersFile = $dataDir . '/orders.json';

            $status = [
                'exists' => file_exists($ordersFile),
                'size' => 0,
                'last_modified' => null,
                'order_count' => 0
            ];

            if ($status['exists']) {
                $status['size'] = filesize($ordersFile);
                $status['last_modified'] = date('Y-m-d H:i:s', filemtime($ordersFile));

                // Count orders in JSON file
                $content = file_get_contents($ordersFile);
                if ($content) {
                    $data = json_decode($content, true);
                    if (is_array($data)) {
                        $status['order_count'] = count($data);
                    }
                }
            }

            return $status;
        } catch (\Exception $e) {
            return [
                'exists' => false,
                'size' => 0,
                'last_modified' => null,
                'order_count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    private function validateCSRF(): bool
    {
        $providedToken = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        $sessionToken = $_SESSION['csrf_token'] ?? '';

        return !empty($providedToken) && !empty($sessionToken) && hash_equals($sessionToken, $providedToken);
    }
}
