<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Storage Management - DrxDion Admin</title>
		<link href="/assets/css/bootstrap.min.css" rel="stylesheet" />
		<link href="/assets/css/bootstrap-icons.css" rel="stylesheet" />
		<style>
			:root {
				--primary-color: #6366f1;
				--secondary-color: #f8fafc;
				--success-color: #10b981;
				--warning-color: #f59e0b;
				--danger-color: #ef4444;
				--gray-50: #f9fafb;
				--gray-100: #f3f4f6;
				--gray-200: #e5e7eb;
				--gray-300: #d1d5db;
				--gray-600: #4b5563;
				--gray-800: #1f2937;
				--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
				--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
					0 2px 4px -1px rgba(0, 0, 0, 0.06);
				--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
					0 4px 6px -2px rgba(0, 0, 0, 0.05);
			}
			body {
				background: linear-gradient(
					135deg,
					var(--gray-50) 0%,
					var(--gray-100) 100%
				);
				font-family: "Inter", -apple-system, BlinkMacSystemFont,
					"Segoe UI", Roboto, sans-serif;
				min-height: 100vh;
			}
			.navbar {
				background: linear-gradient(
					135deg,
					var(--primary-color) 0%,
					#4f46e5 100%
				) !important;
				box-shadow: var(--shadow-lg);
				border: none;
			}
			.navbar-brand {
				font-weight: 700;
				font-size: 1.5rem;
				color: white !important;
			}
			.nav-link {
				color: rgba(255, 255, 255, 0.9) !important;
				font-weight: 500;
				transition: all 0.2s ease;
			}
			.nav-link:hover,
			.nav-link.active {
				color: white !important;
				background-color: rgba(255, 255, 255, 0.1) !important;
				border-radius: 6px;
			}
			.storage-container {
				padding: 2rem 0;
			}
			.storage-header {
				background: linear-gradient(
					135deg,
					white 0%,
					var(--gray-50) 100%
				);
				border-radius: 20px;
				padding: 2.5rem;
				margin-bottom: 2rem;
				box-shadow: var(--shadow-lg);
				border: 1px solid var(--gray-200);
				position: relative;
				overflow: hidden;
			}
			.storage-header::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 4px;
				background: linear-gradient(
					90deg,
					var(--primary-color),
					#4f46e5,
					var(--success-color)
				);
			}
			.storage-header h2 {
				font-weight: 800;
				color: var(--gray-800);
				margin: 0 0 1rem 0;
				display: flex;
				align-items: center;
				gap: 0.75rem;
				font-size: 2rem;
			}
			.storage-header h2 i {
				background: linear-gradient(
					135deg,
					var(--primary-color),
					#4f46e5
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}
			.storage-header p {
				color: var(--gray-600);
				margin: 0 0 1rem 0;
				font-size: 1.1rem;
			}
			.status-card {
				background: white;
				border-radius: 12px;
				padding: 1.5rem;
				box-shadow: var(--shadow-md);
				border: 1px solid var(--gray-200);
				margin-bottom: 1.5rem;
				transition: all 0.2s ease;
			}
			.status-card:hover {
				box-shadow: var(--shadow-lg);
				transform: translateY(-2px);
			}
			.metric-row {
				display: flex;
				flex-wrap: wrap;
				gap: 1rem;
				margin-bottom: 1rem;
			}
			.metric {
				background: var(--gray-50);
				padding: 1rem;
				border-radius: 8px;
				border: 1px solid var(--gray-200);
				flex: 1;
				min-width: 200px;
				text-align: center;
			}
			.metric-icon {
				font-size: 1.5rem;
				margin-bottom: 0.5rem;
				display: block;
			}
			.metric-value {
				font-size: 1.5rem;
				font-weight: 700;
				color: var(--gray-800);
				margin-bottom: 0.25rem;
			}
			.metric-label {
				font-size: 0.875rem;
				color: var(--gray-600);
				margin: 0;
			}
			.status-good {
				color: var(--success-color);
			}
			.status-warning {
				color: var(--warning-color);
			}
			.status-error {
				color: var(--danger-color);
			}
			.status-info {
				color: var(--primary-color);
			}
			.table-responsive {
				background: white;
				border-radius: 12px;
				box-shadow: var(--shadow-md);
				overflow: hidden;
			}
			.table {
				margin-bottom: 0;
			}
			.table th {
				background: var(--gray-50);
				border: none;
				color: var(--gray-800);
				font-weight: 600;
				padding: 1rem;
			}
			.table td {
				border-color: var(--gray-200);
				padding: 1rem;
				vertical-align: middle;
			}
			.btn-sync {
				background: linear-gradient(
					135deg,
					var(--success-color) 0%,
					#059669 100%
				);
				border: none;
				color: white;
				font-weight: 600;
				padding: 0.75rem 1.5rem;
				border-radius: 8px;
				transition: all 0.2s ease;
			}
			.btn-sync:hover {
				transform: translateY(-1px);
				box-shadow: var(--shadow-md);
				color: white;
			}
			.btn-sync:disabled {
				background: var(--gray-300);
				cursor: not-allowed;
				transform: none;
			}
			.alert {
				border: none;
				border-radius: 12px;
				padding: 1rem 1.5rem;
			}
			.spinner-border-sm {
				width: 1rem;
				height: 1rem;
			}
			.health-item {
				padding: 0.5rem 0;
				border-bottom: 1px solid var(--gray-200);
			}
			.health-item:last-child {
				border-bottom: none;
			}
			@media (max-width: 768px) {
				.metric-row {
					flex-direction: column;
				}
				.metric {
					min-width: auto;
				}
				.storage-header {
					padding: 1.5rem;
				}
				.storage-header h2 {
					font-size: 1.5rem;
					margin-bottom: 1rem;
				}
			}
		</style>
	</head>
	<body>
		<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
			<div class="container-fluid">
				<a class="navbar-brand" href="/admin/orders">DrxDion Admin</a>
				<button
					class="navbar-toggler"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#navbarNav"
				>
					<span class="navbar-toggler-icon"></span>
				</button>
				<div class="collapse navbar-collapse" id="navbarNav">
					<ul class="navbar-nav">
						<li class="nav-item">
							<a class="nav-link" href="/admin/orders">Orders</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="/admin/settings"
								>Settings</a
							>
						</li>
						<li class="nav-item">
							<a class="nav-link active" href="/admin/storage"
								>Storage</a
							>
						</li>
					</ul>
					<ul class="navbar-nav ms-auto">
						<li class="nav-item">
							<button
								class="btn btn-outline-light"
								onclick="logout()"
							>
								Logout
							</button>
						</li>
					</ul>
				</div>
			</div>
		</nav>

		<div class="container storage-container">
			<!-- Alerts -->
			<div
				id="alerts-container"
				style="
					position: sticky;
					top: 20px;
					z-index: 1050;
					margin-bottom: 1rem;
				"
			></div>

			<!-- Header -->
			<div class="storage-header">
				<h2>
					<i class="bi bi-database"></i>
					Storage Management
				</h2>
				<p class="text-muted mb-0">
					Monitor and manage your data storage systems
				</p>

				<div class="mt-3 d-flex justify-content-end">
					<button class="btn btn-light" onclick="refreshStatus()">
						<i class="bi bi-arrow-clockwise me-2"></i>Refresh
					</button>
				</div>
			</div>

			<!-- Status Cards -->
			<div class="row">
				<!-- Storage Status -->
				<div class="col-lg-6 mb-4">
					<div class="status-card">
						<h5 class="mb-3">
							<i class="bi bi-hdd-stack me-2"></i>Storage Status
						</h5>
						<div id="storage-status">
							<div class="text-center py-3">
								<div
									class="spinner-border text-primary"
									role="status"
								>
									<span class="visually-hidden"
										>Loading...</span
									>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Order Statistics -->
				<div class="col-lg-6 mb-4">
					<div class="status-card">
						<h5 class="mb-3">
							<i class="bi bi-graph-up me-2"></i>Order Statistics
						</h5>
						<div id="order-statistics">
							<div class="text-center py-3">
								<div
									class="spinner-border text-primary"
									role="status"
								>
									<span class="visually-hidden"
										>Loading...</span
									>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Data Consistency -->
			<div class="status-card">
				<div class="row align-items-center">
					<div class="col-md-8">
						<h5 class="mb-2">
							<i class="bi bi-shield-check me-2"></i>Data
							Consistency
						</h5>
						<div id="consistency-status">
							<div class="text-center py-2">
								<div
									class="spinner-border text-primary"
									role="status"
								>
									<span class="visually-hidden"
										>Loading...</span
									>
								</div>
							</div>
						</div>
					</div>
					<div class="col-md-4 text-end">
						<div class="btn-group">
							<button
								id="sync-btn"
								class="btn btn-sync"
								onclick="syncStorage()"
								disabled
							>
								<i class="bi bi-arrow-repeat me-2"></i>Sync Data
							</button>
							<button
								type="button"
								class="btn btn-sync dropdown-toggle dropdown-toggle-split"
								data-bs-toggle="dropdown"
								aria-expanded="false"
								id="sync-dropdown"
								disabled
							>
								<span class="visually-hidden"
									>Toggle Dropdown</span
								>
							</button>
							<ul class="dropdown-menu">
								<li>
									<a
										class="dropdown-item"
										href="#"
										onclick="handleSyncClick(event, 'additive')"
									>
										<i class="bi bi-plus me-2"></i>Additive
										Sync
										<small class="d-block text-muted"
											>Add/update from MySQL to
											JSON</small
										>
									</a>
								</li>
								<li>
									<a
										class="dropdown-item"
										href="#"
										onclick="handleSyncClick(event, 'replace')"
									>
										<i
											class="bi bi-arrow-clockwise me-2"
										></i
										>Replace Sync
										<small class="d-block text-muted"
											>Replace JSON with MySQL data</small
										>
									</a>
								</li>
								<li><hr class="dropdown-divider" /></li>
								<li>
									<a
										class="dropdown-item text-danger"
										href="#"
										onclick="handleSyncClick(event, 'force-replace')"
									>
										<i
											class="bi bi-exclamation-triangle me-2"
										></i
										>Force Replace
										<small class="d-block text-muted"
											>⚠️ Removes extra JSON orders</small
										>
									</a>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>

			<!-- File Status -->
			<div class="status-card">
				<h5 class="mb-3">
					<i class="bi bi-file-earmark-text me-2"></i>Storage Files
				</h5>
				<div id="file-status">
					<div class="text-center py-3">
						<div class="spinner-border text-primary" role="status">
							<span class="visually-hidden">Loading...</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Recent Orders -->
			<div class="status-card">
				<h5 class="mb-3">
					<i class="bi bi-clock-history me-2"></i>Recent Orders
				</h5>
				<div class="table-responsive">
					<table class="table">
						<thead>
							<tr>
								<th>Order ID</th>
								<th>Customer</th>
								<th>Total</th>
								<th>Status</th>
								<th>Date</th>
							</tr>
						</thead>
						<tbody id="recent-orders">
							<tr>
								<td colspan="5" class="text-center py-4">
									<div
										class="spinner-border text-primary"
										role="status"
									>
										<span class="visually-hidden"
											>Loading...</span
										>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!-- System Health -->
			<div class="status-card">
				<h5 class="mb-3">
					<i class="bi bi-heart-pulse me-2"></i>System Health
				</h5>
				<div id="system-health">
					<div class="text-center py-3">
						<div class="spinner-border text-primary" role="status">
							<span class="visually-hidden">Loading...</span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<script src="/assets/js/bootstrap.bundle.min.js"></script>
		<script>
			let storageData = null;

			// Load storage status on page load
			document.addEventListener("DOMContentLoaded", function () {
				loadStorageStatus();
			});

			// Get CSRF token
			async function getCSRFToken() {
				try {
					const response = await fetch("/api/csrf-token");
					const data = await response.json();
					console.log("CSRF Token Response:", data);
					return data.data?.token || data.token;
				} catch (error) {
					console.error("Failed to get CSRF token:", error);
					return null;
				}
			}

			async function loadStorageStatus() {
				try {
					const response = await fetch("/admin/storage/status");
					const result = await response.json();

					if (result.success) {
						storageData = result.data;
						updateStorageDisplay();
					} else {
						showAlert(
							"danger",
							"Failed to load storage status: " +
								(result.error || "Unknown error")
						);
					}
				} catch (error) {
					console.error("Storage status error:", error);
					showAlert(
						"danger",
						"Failed to load storage status: " + error.message
					);
				}
			}

			function updateStorageDisplay() {
				if (!storageData) return;

				console.log("Storage data:", storageData);

				// Update storage status
				updateStorageStatus();
				updateOrderStatistics();
				updateConsistencyStatus();
				updateFileStatus();
				updateRecentOrders();
				updateSystemHealth();
			}

			function updateStorageStatus() {
				const storage = storageData.storage || {};
				const html = `
					<div class="metric-row">
						<div class="metric">
							<span class="metric-icon">🗄️</span>
							<div class="metric-value ${
								storage.mysql_available
									? "status-good"
									: "status-error"
							}">
								${storage.mysql_available ? "Online" : "Offline"}
							</div>
							<p class="metric-label">MySQL Database</p>
						</div>
						<div class="metric">
							<span class="metric-icon">📁</span>
							<div class="metric-value status-good">Online</div>
							<p class="metric-label">JSON Storage</p>
						</div>
						<div class="metric">
							<span class="metric-icon">${
								storage.primary_storage === "mysql"
									? "🗄️"
									: "📁"
							}</span>
							<div class="metric-value status-info">${(
								storage.primary_storage || "Unknown"
							).toUpperCase()}</div>
							<p class="metric-label">Primary Storage</p>
						</div>
					</div>
					<p class="mb-0 text-muted">
						<strong>Repository:</strong> ${storageData.repository_type || "Unknown"}
					</p>
				`;
				document.getElementById("storage-status").innerHTML = html;
			}

			function updateOrderStatistics() {
				const stats = storageData.order_statistics || {};
				const html = `
					<div class="metric-row">
						<div class="metric">
							<span class="metric-icon">📋</span>
							<div class="metric-value">${stats.total_orders || 0}</div>
							<p class="metric-label">Total Orders</p>
						</div>
						<div class="metric">
							<span class="metric-icon">🗄️</span>
							<div class="metric-value status-info">${stats.mysql_count || 0}</div>
							<p class="metric-label">MySQL Orders</p>
						</div>
						<div class="metric">
							<span class="metric-icon">📁</span>
							<div class="metric-value status-info">${stats.json_count || 0}</div>
							<p class="metric-label">JSON Orders</p>
						</div>
						<div class="metric">
							<span class="metric-icon">⏳</span>
							<div class="metric-value status-warning">${stats.pending_orders || 0}</div>
							<p class="metric-label">Pending</p>
						</div>
						<div class="metric">
							<span class="metric-icon">✅</span>
							<div class="metric-value status-good">${stats.completed_orders || 0}</div>
							<p class="metric-label">Completed</p>
						</div>
					</div>
				`;
				document.getElementById("order-statistics").innerHTML = html;
			}

			function updateConsistencyStatus() {
				const consistency = storageData.data_consistency || {};
				const stats = storageData.order_statistics || {};
				console.log("Consistency data:", consistency);
				console.log("Stats data:", stats);
				const isConsistent = consistency.is_consistent;
				const difference = consistency.difference || 0;

				let statusClass = isConsistent
					? "status-good"
					: "status-warning";
				let statusIcon = isConsistent ? "✅" : "⚠️";
				let statusText = isConsistent
					? "Data is consistent"
					: `Data inconsistency detected (${difference} orders difference)`;

				const html = `
					<div class="d-flex align-items-center">
						<span class="me-3" style="font-size: 1.5rem;">${statusIcon}</span>
						<div>
							<div class="fw-bold ${statusClass}">${statusText}</div>
							<small class="text-muted">
								MySQL: ${stats.mysql_count || 0} orders,
								JSON: ${stats.json_count || 0} orders
							</small>
						</div>
					</div>
				`;
				document.getElementById("consistency-status").innerHTML = html;

				// Enable/disable sync button
				const syncBtn = document.getElementById("sync-btn");
				const syncDropdown = document.getElementById("sync-dropdown");
				if (
					storageData.storage &&
					storageData.storage.mysql_available
				) {
					syncBtn.disabled = false;
					if (syncDropdown) syncDropdown.disabled = false;
					if (!isConsistent) {
						syncBtn.innerHTML =
							'<i class="bi bi-exclamation-triangle me-2"></i>Sync Required';
						syncBtn.classList.add("btn-warning");
						syncBtn.classList.remove("btn-sync");
					} else {
						syncBtn.innerHTML =
							'<i class="bi bi-arrow-repeat me-2"></i>Sync Data';
						syncBtn.classList.remove("btn-warning");
						syncBtn.classList.add("btn-sync");
					}
				} else {
					syncBtn.disabled = true;
					if (syncDropdown) syncDropdown.disabled = true;
				}
			}

			function updateFileStatus() {
				const file = storageData.file_status || {};
				console.log("File status data:", file);
				const sizeFormatted = file.size
					? (file.size / 1024).toFixed(2) + " KB"
					: "0 KB";
				const lastModified = file.last_modified
					? file.last_modified
					: "Never";

				const html = `
					<div class="metric-row">
						<div class="metric">
							<span class="metric-icon">📄</span>
							<div class="metric-value ${file.exists ? "status-good" : "status-error"}">
								${file.exists ? "Exists" : "Missing"}
							</div>
							<p class="metric-label">orders.json</p>
						</div>
						<div class="metric">
							<span class="metric-icon">📏</span>
							<div class="metric-value">${sizeFormatted}</div>
							<p class="metric-label">File Size</p>
						</div>
						<div class="metric">
							<span class="metric-icon">🕐</span>
							<div class="metric-value">${lastModified}</div>
							<p class="metric-label">Last Modified</p>
						</div>
						<div class="metric">
							<span class="metric-icon">📋</span>
							<div class="metric-value">${file.order_count || 0}</div>
							<p class="metric-label">JSON Orders</p>
						</div>
					</div>
				`;
				document.getElementById("file-status").innerHTML = html;
			}

			function updateRecentOrders() {
				const orders = storageData.recent_orders || [];

				if (orders.length === 0) {
					document.getElementById("recent-orders").innerHTML = `
						<tr>
							<td colspan="5" class="text-center py-4 text-muted">No orders found</td>
						</tr>
					`;
					return;
				}

				const html = orders
					.map((order) => {
						const statusClass = getStatusClass(
							order.payment_status
						);
						const date = new Date(
							order.created_at
						).toLocaleString();
						return `
						<tr>
							<td><code>${order.id}</code></td>
							<td>${order.customer_name}</td>
							<td>$${parseFloat(order.total_price).toFixed(2)}</td>
							<td><span class="${statusClass}">${order.payment_status}</span></td>
							<td>${date}</td>
						</tr>
					`;
					})
					.join("");

				document.getElementById("recent-orders").innerHTML = html;
			}

			function updateSystemHealth() {
				const healthItems = [];

				// Repository type check
				const repositoryType = storageData.repository_type || "";
				if (
					repositoryType.includes &&
					repositoryType.includes("Smart")
				) {
					healthItems.push(
						"✅ Smart repository active - automatic fallback enabled"
					);
				} else {
					healthItems.push(
						"⚠️ Basic repository - no automatic fallback"
					);
				}

				// Storage availability check
				const storage = storageData.storage || {};
				if (storage.mysql_available && storage.json_available) {
					healthItems.push(
						"✅ Dual storage operational - data safety assured"
					);
				} else if (storage.json_available) {
					healthItems.push("⚠️ JSON-only mode - MySQL unavailable");
				} else {
					healthItems.push("❌ Storage issues detected");
				}

				// Data consistency check
				const consistency = storageData.data_consistency || {};
				if (consistency.is_consistent) {
					healthItems.push(
						"✅ Data consistency good - MySQL and JSON in sync"
					);
				} else {
					healthItems.push(
						"⚠️ Data inconsistency detected - sync recommended"
					);
				}

				const html = healthItems
					.map((item) => `<div class="health-item">${item}</div>`)
					.join("");
				document.getElementById("system-health").innerHTML = html;
			}

			function getStatusClass(status) {
				switch (status) {
					case "pending":
						return "status-warning";
					case "paid":
					case "completed":
						return "status-good";
					case "failed":
					case "cancelled":
						return "status-error";
					default:
						return "status-info";
				}
			}

			async function syncStorage(type = "additive") {
				const syncBtn = document.getElementById("sync-btn");
				const syncDropdown = document.getElementById("sync-dropdown");
				const originalHtml = syncBtn.innerHTML;

				// Ensure dropdown is closed if sync is called directly
				const dropdownToggle = document.querySelector(
					'[data-bs-toggle="dropdown"]'
				);
				if (dropdownToggle) {
					const dropdown =
						bootstrap.Dropdown.getInstance(dropdownToggle);
					if (dropdown) {
						dropdown.hide();
					}
				}

				// Show loading state
				syncBtn.disabled = true;
				if (syncDropdown) syncDropdown.disabled = true;

				const loadingText =
					type === "replace" ? "Replacing..." : "Syncing...";
				syncBtn.innerHTML = `<span class="spinner-border spinner-border-sm me-2"></span>${loadingText}`;

				try {
					// Get CSRF token
					const csrfToken = await getCSRFToken();
					if (!csrfToken) {
						throw new Error("Could not retrieve CSRF token");
					}

					const response = await fetch("/admin/storage/sync", {
						method: "POST",
						headers: {
							"Content-Type": "application/json",
							"X-CSRF-Token": csrfToken,
						},
						body: JSON.stringify({
							_token: csrfToken,
							type: type,
						}),
					});

					const result = await response.json();

					if (result.success) {
						const syncData = result.data || {};
						const syncType = type; // Use the type from the request
						let message = `Sync completed successfully! (${syncType})<br><small>`;

						if (syncType === "replace") {
							message += `• JSON completely replaced with MySQL data<br>`;
							message += `• Total orders synced: ${
								syncData.synced_records || 0
							}<br>`;
							message += `• Total records: ${
								syncData.total_records || "N/A"
							}`;
						} else {
							message += `• ${
								syncData.synced_records || 0
							} orders synced<br>`;
							message += `• Total records: ${
								syncData.total_records || 0
							}<br>`;
							message += `• Duration: ${
								syncData.duration_seconds || "N/A"
							}s`;
						}

						if (syncData.errors && syncData.errors.length > 0) {
							message += `<br>• Errors: ${syncData.errors.length}`;
						}

						message += `</small>`;

						showAlert("success", message);

						// Refresh the status after sync
						setTimeout(() => {
							loadStorageStatus();
						}, 1000);
					} else {
						showAlert(
							"danger",
							"Sync failed: " +
								(result.message ||
									result.error ||
									"Unknown error")
						);
					}
				} catch (error) {
					console.error("Sync error:", error);
					showAlert("danger", "Sync failed: " + error.message);
				} finally {
					// Restore button state
					syncBtn.innerHTML = originalHtml;
					setTimeout(() => {
						syncBtn.disabled = false;
						if (syncDropdown) syncDropdown.disabled = false;
					}, 2000);
				}
			}

			function refreshStatus() {
				showAlert("info", "Refreshing storage status...");
				loadStorageStatus();
			}

			function showAlert(type, message) {
				const alertsContainer =
					document.getElementById("alerts-container");
				const alertId = "alert-" + Date.now();

				const alertHtml = `
					<div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" style="position: relative; z-index: 1050;">
						${message}
						<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
					</div>
				`;

				// Insert at the beginning so newest alerts appear at top
				alertsContainer.insertAdjacentHTML("afterbegin", alertHtml);

				// Scroll to top to show the alert
				window.scrollTo({ top: 0, behavior: "smooth" });

				// Auto-remove after 8 seconds for success/info alerts
				if (type === "success" || type === "info") {
					setTimeout(() => {
						const alert = document.getElementById(alertId);
						if (alert) {
							const bsAlert = new bootstrap.Alert(alert);
							bsAlert.close();
						}
					}, 8000);
				} else if (type === "danger") {
					// Keep error alerts for 12 seconds
					setTimeout(() => {
						const alert = document.getElementById(alertId);
						if (alert) {
							const bsAlert = new bootstrap.Alert(alert);
							bsAlert.close();
						}
					}, 12000);
				}
			}

			function handleSyncClick(event, type) {
				// Prevent default link behavior
				event.preventDefault();

				// Close the dropdown menu - improved method
				const dropdownMenu = event.target.closest(".dropdown-menu");
				const dropdownToggle = dropdownMenu.previousElementSibling;

				// Use Bootstrap's API to hide the dropdown
				const dropdown =
					bootstrap.Dropdown.getOrCreateInstance(dropdownToggle);
				dropdown.hide();

				// Small delay to ensure dropdown closes before sync starts
				setTimeout(() => {
					if (type === "force-replace") {
						confirmReplaceSync();
					} else {
						syncStorage(type);
					}
				}, 100);
			}

			function logout() {
				if (confirm("Are you sure you want to logout?")) {
					window.location.href = "/admin/logout";
				}
			}

			function confirmReplaceSync() {
				// Ensure dropdown is closed
				const dropdownToggle = document.querySelector(
					'[data-bs-toggle="dropdown"]'
				);
				if (dropdownToggle) {
					const dropdown =
						bootstrap.Dropdown.getInstance(dropdownToggle);
					if (dropdown) {
						dropdown.hide();
					}
				}

				if (
					confirm(
						"⚠️ WARNING: This will completely replace JSON data with MySQL data.\n\nAny orders that exist only in JSON (like fallback orders) will be permanently deleted.\n\nAre you sure you want to continue?"
					)
				) {
					syncStorage("replace");
				}
			}
		</script>
	</body>
</html>
