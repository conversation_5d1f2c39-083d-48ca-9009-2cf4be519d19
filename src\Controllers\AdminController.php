<?php

declare(strict_types=1);

namespace DrxDion\Controllers;

use DrxDion\Database\MySQL\MySQLSettingsRepository;
use DrxDion\Services\StorageService;
use DrxDion\Database\MySQL\MySQLAdminRepository;
use DrxDion\Interfaces\OrderRepositoryInterface;

/**
 * Admin Controller
 * Handles admin panel API endpoints
 */
final class AdminController
{
    public function __construct(
        private readonly MySQLSettingsRepository $settingsRepository,
        private readonly StorageService $storageService,
        private readonly MySQLAdminRepository $adminRepository,
        private readonly OrderRepositoryInterface $orderRepository
    ) {}

    public function getSettings(array $request): void
    {
        header('Content-Type: application/json');

        try {
            $settings = $this->settingsRepository->getAll();

            // Convert to simple key-value format for frontend
            $settingsData = [];
            foreach ($settings as $setting) {
                $settingsData[$setting->getKey()] = $setting->getValue();
            }

            // Provide default values if settings don't exist
            $defaultSettings = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'notification_email' => '<EMAIL>',
                'site_name' => 'DrxDion',
                'timezone' => 'Europe/Istanbul'
            ];

            $result = array_merge($defaultSettings, $settingsData);

            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to load settings: ' . $e->getMessage()
            ]);
        }
    }

    public function updateSettings(array $request): void
    {
        header('Content-Type: application/json');

        try {
            $data = $request['body'] ?? [];

            if (empty($data)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'No data provided'
                ]);
                return;
            }

            // Handle password update separately if provided
            if (!empty($data['new_password'])) {
                if (empty($data['current_password'])) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'Current password is required to change password'
                    ]);
                    return;
                }

                // Verify current password
                $currentUser = $this->adminRepository->findByUsername($data['username'] ?? 'admin');
                if (!$currentUser || !password_verify($data['current_password'], $currentUser->getPasswordHash())) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'Current password is incorrect'
                    ]);
                    return;
                }

                // Update password
                $hashedPassword = password_hash($data['new_password'], PASSWORD_DEFAULT);
                $this->adminRepository->updatePassword($currentUser->getId(), $hashedPassword);
            }

            // Update other settings
            $settingsToUpdate = [];
            $allowedSettings = ['username', 'email', 'notification_email', 'site_name', 'timezone'];

            foreach ($allowedSettings as $key) {
                if (isset($data[$key])) {
                    $settingsToUpdate[$key] = $data[$key];
                }
            }

            if (!empty($settingsToUpdate)) {
                $this->settingsRepository->updateMultiple($settingsToUpdate);
            }

            echo json_encode([
                'success' => true,
                'message' => 'Settings updated successfully'
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to update settings: ' . $e->getMessage()
            ]);
        }
    }

    public function getStorageStatus(array $request): void
    {
        header('Content-Type: application/json');

        try {
            $status = $this->storageService->getStatus();
            $healthMetrics = $this->storageService->getHealthMetrics();
            $syncStats = $this->storageService->getSyncStats();

            // Get recent orders for display
            $recentOrders = $this->getRecentOrders();

            // Get file status information
            $fileStatus = $this->getFileStatus();

            // Build response in the format expected by JavaScript
            $responseData = [
                'repository_type' => 'Smart Repository', // Default to Smart for now
                'storage' => [
                    'mysql_available' => $status['mysql']['connected'] ?? false,
                    'json_available' => $status['json']['connected'] ?? false,
                    'primary_storage' => ($status['mysql']['connected'] ?? false) ? 'mysql' : 'json'
                ],
                'data_consistency' => [
                    'is_consistent' => !($syncStats['sync_needed'] ?? true),
                    'difference' => $syncStats['difference'] ?? 0
                ],
                'order_statistics' => [
                    'mysql_count' => $syncStats['mysql_records'] ?? 0,
                    'json_count' => $syncStats['json_records'] ?? 0,
                    'total_orders' => max($syncStats['mysql_records'] ?? 0, $syncStats['json_records'] ?? 0),
                    'pending_orders' => 0, // Will be calculated from recent orders
                    'completed_orders' => 0 // Will be calculated from recent orders
                ],
                'recent_orders' => $recentOrders,
                'file_status' => $fileStatus,
                'last_sync' => $status['last_sync']
            ];

            echo json_encode([
                'success' => true,
                'data' => $responseData
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to get storage status: ' . $e->getMessage()
            ]);
        }
    }

    private function getRecentOrders(): array
    {
        try {
            // Get recent orders from the order repository
            $orders = $this->orderRepository->findAll();

            // Sort by created_at descending and take first 10
            usort($orders, function ($a, $b) {
                $dateA = $a->getCreatedAt() ?? new \DateTime('1970-01-01');
                $dateB = $b->getCreatedAt() ?? new \DateTime('1970-01-01');
                return $dateB <=> $dateA;
            });

            $recentOrders = array_slice($orders, 0, 10);

            // Convert to array format expected by JavaScript
            return array_map(function ($order) {
                return [
                    'id' => $order->getId(),
                    'customer_name' => $order->getCustomerName(),
                    'total_price' => $order->getTotalPrice(),
                    'payment_status' => $order->getPaymentStatus(),
                    'created_at' => $order->getCreatedAt() ? $order->getCreatedAt()->format('Y-m-d H:i:s') : null
                ];
            }, $recentOrders);
        } catch (\Exception $e) {
            return [];
        }
    }

    private function getFileStatus(): array
    {
        try {
            $dataDir = __DIR__ . '/../../data';
            $ordersFile = $dataDir . '/orders.json';

            $status = [
                'exists' => file_exists($ordersFile),
                'size' => 0,
                'last_modified' => null,
                'order_count' => 0
            ];

            if ($status['exists']) {
                $status['size'] = filesize($ordersFile);
                $status['last_modified'] = date('Y-m-d H:i:s', filemtime($ordersFile));

                // Count orders in JSON file
                $content = file_get_contents($ordersFile);
                if ($content) {
                    $data = json_decode($content, true);
                    if (is_array($data)) {
                        $status['order_count'] = count($data);
                    }
                }
            }

            return $status;
        } catch (\Exception $e) {
            return [
                'exists' => false,
                'size' => 0,
                'last_modified' => null,
                'order_count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    public function syncStorage(array $request): void
    {
        header('Content-Type: application/json');

        try {
            // CSRF protection
            if (!$this->validateCSRF($request)) {
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid CSRF token'
                ]);
                return;
            }

            $data = $request['body'] ?? [];
            $forceReplace = ($data['force_replace'] ?? false) === true;

            $result = $this->storageService->sync($forceReplace);

            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Storage sync completed successfully',
                    'data' => $result
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => $result['error'] ?? 'Sync failed',
                    'data' => $result
                ]);
            }
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to sync storage: ' . $e->getMessage()
            ]);
        }
    }

    private function validateCSRF(array $request): bool
    {
        $headers = $request['headers'] ?? [];
        $providedToken = $headers['X-CSRF-Token'] ?? $headers['x-csrf-token'] ?? '';
        $sessionToken = $_SESSION['csrf_token'] ?? '';

        return !empty($providedToken) && !empty($sessionToken) && hash_equals($sessionToken, $providedToken);
    }
}
